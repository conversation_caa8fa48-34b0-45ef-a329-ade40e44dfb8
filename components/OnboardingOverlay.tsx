import Text6 from '@/components/CustomText';
import AntDesign from '@expo/vector-icons/AntDesign';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Animated,
  Dimensions,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  targetElement?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  position?: 'top' | 'bottom' | 'center';
  showSkip?: boolean;
}

interface OnboardingOverlayProps {
  visible: boolean;
  steps: OnboardingStep[];
  currentStepIndex: number;
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  onComplete: () => void;
}

const OnboardingOverlay: React.FC<OnboardingOverlayProps> = ({
  visible,
  steps,
  currentStepIndex,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
}) => {
  const { t } = useTranslation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const [contentPosition, setContentPosition] = useState<'top' | 'bottom' | 'center'>('center');

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  useEffect(() => {
    if (currentStep?.targetElement) {
      // Determine content position based on target element position
      const targetY = currentStep.targetElement.y;
      const targetHeight = currentStep.targetElement.height;
      const targetBottom = targetY + targetHeight;
      
      if (currentStep.position) {
        setContentPosition(currentStep.position);
      } else if (targetY < screenHeight / 3) {
        setContentPosition('bottom');
      } else if (targetBottom > (screenHeight * 2) / 3) {
        setContentPosition('top');
      } else {
        setContentPosition('center');
      }
    } else {
      setContentPosition(currentStep?.position || 'center');
    }
  }, [currentStep]);

  const handleNext = () => {
    if (isLastStep) {
      onComplete();
    } else {
      onNext();
    }
  };

  const renderHighlight = () => {
    if (!currentStep?.targetElement) return null;

    const { x, y, width, height } = currentStep.targetElement;
    const highlightPadding = 8;

    return (
      <View
        style={[
          styles.highlight,
          {
            left: x - highlightPadding,
            top: y - highlightPadding,
            width: width + highlightPadding * 2,
            height: height + highlightPadding * 2,
          },
        ]}
      />
    );
  };

  const renderContent = () => {
    const getContentStyle = () => {
      switch (contentPosition) {
        case 'top':
          return styles.contentTop;
        case 'bottom':
          return styles.contentBottom;
        default:
          return styles.contentCenter;
      }
    };

    return (
      <Animated.View
        style={[
          styles.content,
          getContentStyle(),
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <View style={styles.contentHeader}>
          <Text6 weight="medium" style={styles.stepCounter}>
            {currentStepIndex + 1} / {steps.length}
          </Text6>
          {currentStep?.showSkip !== false && (
            <TouchableOpacity onPress={onSkip} style={styles.skipButton}>
              <Text6 style={styles.skipText}>{t('onboarding.skip')}</Text6>
            </TouchableOpacity>
          )}
        </View>

        <Text6 weight="medium" style={styles.title}>
          {currentStep?.title}
        </Text6>

        <Text6 style={styles.description}>
          {currentStep?.description}
        </Text6>

        <View style={styles.buttonContainer}>
          {!isFirstStep && (
            <TouchableOpacity onPress={onPrevious} style={styles.secondaryButton}>
              <AntDesign name="left" size={16} color="#666" />
              <Text6 style={styles.secondaryButtonText}>{t('onboarding.previous')}</Text6>
            </TouchableOpacity>
          )}

          <TouchableOpacity onPress={handleNext} style={styles.primaryButton}>
            <Text6 weight="medium" style={styles.primaryButtonText}>
              {isLastStep ? t('onboarding.complete') : t('onboarding.next')}
            </Text6>
            {!isLastStep && <AntDesign name="right" size={16} color="#fff" />}
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (!visible || !currentStep) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        {renderHighlight()}
        {renderContent()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  highlight: {
    position: 'absolute',
    borderRadius: 12,
    borderWidth: 3,
    borderColor: '#FB9E3A',
    backgroundColor: 'transparent',
    shadowColor: '#FB9E3A',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 8,
  },
  content: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  contentTop: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
  },
  contentBottom: {
    position: 'absolute',
    bottom: 120,
    left: 20,
    right: 20,
  },
  contentCenter: {
    position: 'absolute',
    top: '50%',
    left: 20,
    right: 20,
    transform: [{ translateY: -100 }],
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepCounter: {
    fontSize: 14,
    color: '#FB9E3A',
  },
  skipButton: {
    padding: 4,
  },
  skipText: {
    fontSize: 14,
    color: '#666',
  },
  title: {
    fontSize: 20,
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#FB9E3A',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginLeft: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    color: '#fff',
    marginRight: 8,
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
  },
  secondaryButtonText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
});

export default OnboardingOverlay;
