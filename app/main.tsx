import Text6 from '@/components/CustomText';
import IngredientSearch from '@/components/IngredientSearch';
import LanguageDropdown from '@/components/LanguageDropdown';
import LoadingOverlay from '@/components/LoadingOverlay';
import MainScreenOnboarding from '@/components/MainScreenOnboarding';
import { createRewardedAd, getBannerAdId, getBannerAdSize, initializeAds, showRewardedAd } from '@/utils/adManager';
import { requestReview } from '@/utils/appStoreReview';
import { generateRecipe, validateIngredients } from '@/utils/recipeGenerator';
import { incrementSearchUsageCount, markReviewPromptAsShown, shouldShowReviewPrompt } from '@/utils/searchUsageTracker';
import { DifficultyLevel, isOnboardingCompleted, ONBOARDING_PAGES, saveRecipe } from '@/utils/storage';
import AntDesign from '@expo/vector-icons/AntDesign';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Modal, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { BannerAd } from 'react-native-google-mobile-ads';

export default function MainScreen() {
  const { t } = useTranslation();
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('Beginner');
  const [showDifficultyModal, setShowDifficultyModal] = useState(false);
  const [adsInitialized, setAdsInitialized] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showLoadingOverlay, setShowLoadingOverlay] = useState(false);
  const [rewardedAdLoaded, setRewardedAdLoaded] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Refs for onboarding element positioning
  const searchContainerRef = useRef<View>(null);
  const difficultyContainerRef = useRef<View>(null);
  const generateButtonRef = useRef<View>(null);

  useEffect(() => {
    const initAds = async () => {
      try {
        await initializeAds();
        setAdsInitialized(true);

        // Pre-load rewarded ad
        try {
          await createRewardedAd();
          setRewardedAdLoaded(true);
        } catch (adError) {
          console.error('Failed to load rewarded ad:', adError);
        }
      } catch (error) {
        console.error('Failed to initialize ads:', error);
      }
    };
    initAds();
  }, []);

  // Check onboarding status on component mount
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const isCompleted = await isOnboardingCompleted(ONBOARDING_PAGES.MAIN_SCREEN);
        if (!isCompleted) {
          // Delay showing onboarding to ensure UI is rendered
          setTimeout(() => {
            setShowOnboarding(true);
          }, 500);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
      }
    };
    checkOnboardingStatus();
  }, []);

  const handleBookPress = () => {
    router.push('/recipe_list');
  };

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  const handleOnboardingSkip = () => {
    setShowOnboarding(false);
  };

  const handleGenerateRecipe = async () => {
    if (!validateIngredients(ingredients)) {
      Alert.alert(t('main.errorTitle'), t('main.noIngredientsError') || 'Please add at least one ingredient to generate a recipe.');
      return;
    }

    try {
      // Increment search usage count
      await incrementSearchUsageCount();

      // Show video ad if available
      if (rewardedAdLoaded) {
        try {
          await showRewardedAd();
          // Reload ad for next time
          setRewardedAdLoaded(false);
          createRewardedAd()
            .then(() => setRewardedAdLoaded(true))
            .catch(console.error);
        } catch (adError) {
          console.error('Error showing video ad:', adError);
        }
      }

      // Show loading overlay
      setShowLoadingOverlay(true);
      setIsGenerating(true);

      const recipe = await generateRecipe({
        ingredients,
        difficulty,
        language: t('main.language') === 'Korean' ? 'ko' : 'en',
      });

      await saveRecipe(recipe);

      // Hide loading overlay
      setShowLoadingOverlay(false);

      // Navigate to the generated recipe
      router.push(`/recipe_detail?id=${recipe.id}`);

      // Check if we should show review prompt
      if (await shouldShowReviewPrompt()) {
        setTimeout(() => {
          requestReview(
            async () => {
              await markReviewPromptAsShown();
            },
            async () => {
              await markReviewPromptAsShown();
            }
          );
        }, 1000); // Show review prompt after a short delay
      }
    } catch (error) {
      console.error('Error generating recipe:', error);
      setShowLoadingOverlay(false);
      Alert.alert(t('main.errorTitle'), t('main.networkError') || 'Failed to generate recipe. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Top row with two icons spaced between */}
      <View style={styles.topRow}>
        <LanguageDropdown iconSize={24} iconColor="#333" />
        <TouchableOpacity onPress={handleBookPress} activeOpacity={0.7}>
          <AntDesign name="book" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Header Text */}
        <View style={styles.headerContainer}>
          <Text6 weight="medium" style={styles.taglineText}>
            {t('main.tagline')}
          </Text6>
        </View>

        {/* Ingredient Search */}
        <View ref={searchContainerRef} style={styles.searchContainer}>
          <IngredientSearch ingredients={ingredients} onIngredientsChange={setIngredients} maxIngredients={5} />
        </View>

        {/* Difficulty Selector */}
        <View ref={difficultyContainerRef} style={styles.difficultyContainer}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('recipeEdit.difficulty')}
          </Text6>
          <TouchableOpacity style={styles.difficultySelector} onPress={() => setShowDifficultyModal(true)} activeOpacity={0.7}>
            <Text6 style={styles.difficultyText}>{t(`recipeEdit.difficultyLevels.${difficulty}`)}</Text6>
            <AntDesign name="down" size={16} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Generate Recipe Button */}
        <View style={styles.generateButtonContainer}>
          <TouchableOpacity
            ref={generateButtonRef}
            style={[styles.generateButton, isGenerating && styles.generateButtonDisabled]}
            onPress={handleGenerateRecipe}
            disabled={isGenerating}
            activeOpacity={0.8}
          >
            <AntDesign name="search1" size={20} color={isGenerating ? '#ccc' : '#fff'} style={styles.generateButtonIcon} />
            <Text6 weight="medium" style={[styles.generateButtonText, isGenerating && styles.generateButtonTextDisabled]}>
              {isGenerating ? t('main.generating.title') || 'Generating...' : t('main.generateRecipe.title') || 'Generate Recipe'}
            </Text6>
          </TouchableOpacity>
        </View>

        {/* Spacer for ad banner */}
        <View style={styles.contentSpacer} />
      </ScrollView>

      {/* Google Ads Banner */}
      <View style={styles.adBanner}>
        {adsInitialized ? (
          <BannerAd
            unitId={getBannerAdId()}
            size={getBannerAdSize()}
            requestOptions={{
              requestNonPersonalizedAdsOnly: true,
            }}
          />
        ) : (
          <Text6 style={styles.adPlaceholder}>Loading Ads...</Text6>
        )}
      </View>

      {/* Difficulty Selection Modal */}
      <Modal visible={showDifficultyModal} transparent={true} animationType="fade" onRequestClose={() => setShowDifficultyModal(false)}>
        <TouchableOpacity style={styles.modalOverlay} activeOpacity={1} onPress={() => setShowDifficultyModal(false)}>
          <View style={styles.modalContent}>
            <Text6 weight="medium" style={styles.modalTitle}>
              {t('recipeEdit.difficulty')}
            </Text6>
            {(['Beginner', 'Intermediate', 'Advanced', 'Chef'] as DifficultyLevel[]).map((option) => (
              <TouchableOpacity
                key={option}
                style={[styles.difficultyOption, difficulty === option && styles.selectedDifficultyOption]}
                onPress={() => {
                  setDifficulty(option);
                  setShowDifficultyModal(false);
                }}
                activeOpacity={0.7}
              >
                <Text6
                  weight={difficulty === option ? 'medium' : 'regular'}
                  style={[styles.difficultyOptionText, difficulty === option && styles.selectedDifficultyOptionText]}
                >
                  {t(`recipeEdit.difficultyLevels.${option}`)}
                </Text6>
                {difficulty === option && <AntDesign name="check" size={16} color="#FB9E3A" />}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Loading Overlay */}
      <LoadingOverlay
        visible={showLoadingOverlay}
        message={t('main.generating') || 'Generating recipe...'}
        onRequestClose={() => setShowLoadingOverlay(false)}
      />

      {/* Onboarding Overlay */}
      <MainScreenOnboarding
        visible={showOnboarding}
        onComplete={handleOnboardingComplete}
        onSkip={handleOnboardingSkip}
        searchContainerRef={searchContainerRef}
        difficultyContainerRef={difficultyContainerRef}
        generateButtonRef={generateButtonRef}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60, // Safe area padding for status bar
    paddingBottom: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    alignItems: 'center',
  },
  taglineText: {
    fontSize: 24,
    color: '#333',
    textAlign: 'center',
    lineHeight: 32,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
    position: 'relative',
    zIndex: 1000,
  },
  difficultyContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
  },
  difficultySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  difficultyText: {
    fontSize: 16,
    color: '#333',
  },
  generateButtonContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  generateButton: {
    backgroundColor: '#FB9E3A',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FB9E3A',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  generateButtonDisabled: {
    backgroundColor: '#ccc',
    shadowOpacity: 0,
    elevation: 0,
  },
  generateButtonIcon: {
    marginRight: 8,
  },
  generateButtonText: {
    fontSize: 18,
    color: '#fff',
  },
  generateButtonTextDisabled: {
    color: '#999',
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: '#333',
  },
  searchButton: {
    backgroundColor: '#FB9E3A',
    borderRadius: 8,
    padding: 10,
    marginLeft: 8,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1001,
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: 'white',
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  suggestionText: {
    fontSize: 16,
    color: '#333',
  },
  ingredientsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  ingredientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5E6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#FB9E3A',
  },
  ingredientText: {
    fontSize: 14,
    color: '#FB9E3A',
    marginRight: 6,
  },
  removeButton: {
    padding: 2,
  },
  contentSpacer: {
    height: 100, // Space for ad banner
  },
  adBanner: {
    height: 80,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  adPlaceholder: {
    fontSize: 16,
    color: '#666',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    minWidth: 280,
    maxWidth: '80%',
  },
  modalTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  difficultyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedDifficultyOption: {
    backgroundColor: '#FFF5E6',
  },
  difficultyOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedDifficultyOptionText: {
    color: '#FB9E3A',
  },
});
